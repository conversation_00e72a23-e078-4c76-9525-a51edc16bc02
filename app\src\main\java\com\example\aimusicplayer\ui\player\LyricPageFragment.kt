package com.example.aimusicplayer.ui.player

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.aimusicplayer.R
import com.example.aimusicplayer.ui.player.LyricView
import android.util.Log

/**
 * 歌词页面Fragment
 * 用于在ViewPager2中显示歌词
 */
class LyricPageFragment : Fragment() {

    private val TAG = "LyricPageFragment"
    private var lyricView: LyricView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.d(TAG, "创建歌词页面视图")

        // 创建LyricView
        lyricView = LyricView(requireContext()).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            // 设置默认歌词
            setDefaultLyrics()
        }

        return lyricView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "歌词页面视图创建完成")
    }

    /**
     * 获取LyricView
     */
    fun getLyricView(): LyricView? {
        return lyricView
    }

    /**
     * 设置默认歌词
     */
    private fun setDefaultLyrics() {
        lyricView?.let { view ->
            try {
                // 创建默认歌词列表
                val defaultLyrics = listOf(
                    com.example.aimusicplayer.data.model.LyricLine(0, "欢迎使用AI音乐播放器", null),
                    com.example.aimusicplayer.data.model.LyricLine(2000, "正在加载歌词...", null),
                    com.example.aimusicplayer.data.model.LyricLine(4000, "请稍候", null)
                )

                view.setLyrics(defaultLyrics)
                Log.d(TAG, "设置默认歌词成功")
            } catch (e: Exception) {
                Log.e(TAG, "设置默认歌词失败", e)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        lyricView = null
        Log.d(TAG, "歌词页面视图销毁")
    }
}
